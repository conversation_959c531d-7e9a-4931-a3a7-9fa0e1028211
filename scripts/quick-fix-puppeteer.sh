#!/bin/bash

# Quick fix script for Puppeteer Chrome dependencies issue
# Run this on your production server to immediately fix the issue

echo "🔧 Quick fix for Puppeteer Chrome dependencies..."

# Install the most critical missing dependencies
sudo apt-get update && sudo apt-get install -y \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libgtk-3-0 \
    libgtk-4-1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2

echo "✅ Critical dependencies installed!"
echo "🔄 Please restart your PM2 application:"
echo "   pm2 restart doc-app"
echo ""
echo "🧪 Test the image report with:"
echo "   curl -X POST http://localhost:3000/api/sales/v1/trigger-daily-report-image"

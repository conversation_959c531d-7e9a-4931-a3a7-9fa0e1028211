import { WebClient, Block, KnownBlock } from '@slack/web-api';
import puppeteer from 'puppeteer';
import fs from 'fs';
// import { Readable } from 'stream';
import path from 'path';

import config from '../config';
import { db } from '../utils/db';
import { DateTime } from 'luxon';
import { logger } from '../config/logger';

interface CustomerPayment {
  name: string;
  email: string;
  status: 'succeeded' | 'failed' | 'pending';
  description: string;
  timestamp: string;
  amountCents: number;
}



interface DailySalesData {
  date: string;
  totalPayments: number;
  successfulPayments: number;
  totalAmountCents: number;
  customers: CustomerPayment[];
}

// New interfaces for the updated daily report
interface PeriodSummary {
  period: string;                    // "Past 24 hrs", "Past 7 days", "Past 28 days"
  orders: number;                    // Current period order count
  avgOrderValue: number;             // Current period average (in dollars)
  previousPeriodOrders: number;      // Previous period order count
  previousAvgOrderValue: number;     // Previous period average (in dollars)
  deltaOrders: number;               // Change in orders
  deltaOrdersPercent: number;        // Percentage change in orders
  deltaAvgOrderValue: number;        // Change in average order value
  deltaAvgOrderValuePercent: number; // Percentage change in avg order value
}

interface NewDailyReportData {
  reportDate: string;
  reportTime: string;
  periodSummaries: PeriodSummary[];
}

interface PeriodData {
  salesCount: number;
  totalRevenueCents: number;
  averageOrderValueCents: number;
}

// interface ComparisonData {
//   paymentCountChange: number;
//   paymentCountPercentChange: number;
//   amountChange: number;
//   amountPercentChange: number;
// }

interface WeeklySalesData extends DailySalesData {
  weekStart: string;
  weekEnd: string;
  weekNumber: number;
  dailyBreakdown: DailySalesData[];
  // comparisonToPreviousWeek: ComparisonData;
}

interface MonthlySalesData extends DailySalesData {
  monthStart: string;
  monthEnd: string;
  weeklyBreakdown: WeeklySalesData[];
  // comparisonToPreviousMonth: ComparisonData;
}

interface StripePaymentData {
  stripePaymentIntentId: string;
  customerName: string;
  customerEmail: string;
  amountCents: number;
  currency: string;
  status: string;
  description: string;
  metadata: Record<string, unknown>;
}

class SalesReportingService {
  private slack: WebClient;

  constructor() {
    this.slack = new WebClient(config.slackToken);
  }

  /**
   * Generate a PNG image from HTML using Puppeteer
   */
  private async generateReportImageFromHtml(html: string): Promise<Buffer> {
    const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });
    const element = await page.$('.report-container');
    if (!element) throw new Error('Could not find .report-container in HTML');
    const imageBuffer = await element.screenshot({ type: 'png' });
    await browser.close();
    return imageBuffer as Buffer;
  }

  /**
   * Upload an image buffer to Slack
   */
  // private async sendImageToSlack(imageBuffer: Buffer, title: string, initialComment: string) {
  //   const slackChannel = config.slackSalesReportChannel;
  //   if (!slackChannel) throw new Error('No Slack sales report channel configured');
  //   await this.slack.files.upload({
  //     channels: slackChannel,
  //     file: Readable.from(imageBuffer), // Use a Readable stream for compatibility
  //     filename: `${title.replace(/\s+/g, '_').toLowerCase()}.png`,
  //     filetype: 'png',
  //     title,
  //     initial_comment: initialComment,
  //   });
  // }

  /**
   * Generate and send the daily sales report as an image to Slack (using public URL)
   */
  async sendDailySalesReportAsImage(): Promise<void> {
    try {
      if (!config.salesReportingEnabled) {
        logger.info('Sales reporting is disabled, skipping daily report');
        return;
      }
      logger.info('Generating daily sales report as image');
      const reportData = await this.generateNewDailyReportData();
      const html = this.generateReportHtml(reportData);
      const imageBuffer = await this.generateReportImageFromHtml(html);
      // Save image to assets folder
      const fileName = `report-${reportData.reportDate}.png`;
      const filePath = path.join(__dirname, 'assets', fileName);
      fs.writeFileSync(filePath, imageBuffer);
      // Construct public URL (assuming /api/assets/ is served statically)
      const publicUrl = `${config.publicBaseUrl || 'https://doctor.zenith.clinic/api'}/assets/${fileName}`;
      // Post Slack message with image block
      const slackChannel = config.slackSalesReportChannel;
      if (!slackChannel) throw new Error('No Slack sales report channel configured');
      await this.slack.chat.postMessage({
        channel: slackChannel,
        blocks: [
          {
            type: 'image',
            image_url: publicUrl,
            alt_text: 'Daily Sales Report',
          }
        ],
        text: `Daily Sales Report (sent at midnight AEST)\nReport Date: ${reportData.reportDate} at ${reportData.reportTime}`
      });
      logger.info(`Daily sales report image sent successfully for ${reportData.reportDate}`);
    } catch (error) {
      logger.error('Failed to send daily sales report as image:', error);
      throw error;
    }
  }

  /**
   * Generate HTML for the sales report table
   */
  private generateReportHtml(data: NewDailyReportData): string {
    // Generate date ranges for each period
    const now = DateTime.now().setZone('Australia/Sydney');
    const dateRanges = {
      'Past 24 hrs': {
        current: now.minus({ days: 1 }).toFormat('yyyy-MM-dd'),
        previous: now.minus({ days: 2 }).toFormat('yyyy-MM-dd')
      },
      'Past 7 days': {
        current: `${now.minus({ days: 7 }).toFormat('yyyy-MM-dd')} to ${now.toFormat('yyyy-MM-dd')}`,
        previous: `${now.minus({ days: 14 }).toFormat('yyyy-MM-dd')} to ${now.minus({ days: 7 }).toFormat('yyyy-MM-dd')}`
      },
      'Past 28 days': {
        current: `${now.minus({ days: 28 }).toFormat('yyyy-MM-dd')} to ${now.toFormat('yyyy-MM-dd')}`,
        previous: `${now.minus({ days: 56 }).toFormat('yyyy-MM-dd')} to ${now.minus({ days: 28 }).toFormat('yyyy-MM-dd')}`
      }
    };

    // Professional HTML table with modern styling
    const tableRows = data.periodSummaries.map(summary => {
      const totalSalesCurrent = summary.orders * summary.avgOrderValue;
      const totalSalesPrevious = summary.previousPeriodOrders * summary.previousAvgOrderValue;
      const totalSalesChangePercent = totalSalesPrevious !== 0 ? (totalSalesCurrent - totalSalesPrevious) / totalSalesPrevious * 100 : 0;
      const ordersChangePercent = summary.previousPeriodOrders !== 0 ? ((summary.orders - summary.previousPeriodOrders) / summary.previousPeriodOrders) * 100 : 0;
      const avgOrderChangePercent = summary.previousAvgOrderValue !== 0 ? ((summary.avgOrderValue - summary.previousAvgOrderValue) / summary.previousAvgOrderValue) * 100 : 0;

      const ranges = dateRanges[summary.period as keyof typeof dateRanges];

      // Helper function to get change class
      const getChangeClass = (percent: number) => {
        if (percent > 0) return 'change-positive';
        if (percent < 0) return 'change-negative';
        return 'change-neutral';
      };

      return `
        <tr><td colspan="4" class="period-header">${summary.period}</td></tr>
        <tr><td colspan="4" class="date-range">Current: ${ranges.current} | Previous: ${ranges.previous}</td></tr>
        <tr>
          <th class="metric-header" style="width:25%;">Metric</th>
          <th class="metric-header" style="width:25%;">Previous Period</th>
          <th class="metric-header" style="width:25%;">Current Period</th>
          <th class="metric-header" style="width:25%;">Change (%)</th>
        </tr>
        <tr class="metric-row">
          <td class="metric-name">Orders</td>
          <td class="metric-value">${summary.previousPeriodOrders.toLocaleString()}</td>
          <td class="metric-value">${summary.orders.toLocaleString()}</td>
          <td class="metric-value ${getChangeClass(ordersChangePercent)}">${ordersChangePercent >= 0 ? '+' : ''}${ordersChangePercent.toFixed(1)}%</td>
        </tr>
        <tr class="metric-row">
          <td class="metric-name">Avg Order Value</td>
          <td class="metric-value">$${summary.previousAvgOrderValue.toFixed(2)}</td>
          <td class="metric-value">$${summary.avgOrderValue.toFixed(2)}</td>
          <td class="metric-value ${getChangeClass(avgOrderChangePercent)}">${avgOrderChangePercent >= 0 ? '+' : ''}${avgOrderChangePercent.toFixed(1)}%</td>
        </tr>
        <tr class="metric-row">
          <td class="metric-name">Total Sales</td>
          <td class="metric-value">$${totalSalesPrevious.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
          <td class="metric-value">$${totalSalesCurrent.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
          <td class="metric-value ${getChangeClass(totalSalesChangePercent)}">${totalSalesChangePercent >= 0 ? '+' : ''}${totalSalesChangePercent.toFixed(1)}%</td>
        </tr>
      `;
    }).join('');
    return `
      <html>
        <head>
          <meta charset="utf-8" />
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
              color: #333;
              margin: 0;
              padding: 30px;
              min-height: 100vh;
            }
            .report-container {
              background: #fff;
              border-radius: 12px;
              box-shadow: 0 20px 40px rgba(0,0,0,0.1);
              overflow: hidden;
              max-width: 900px;
              margin: 0 auto;
            }
            .report-header {
              background: linear-gradient(135deg, #1e8449 0%, #27ae60 100%);
              color: #fff;
              padding: 25px 30px;
              text-align: center;
              font-size: 20px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-bottom: 3px solid #2ecc71;
            }
            .report-subtitle {
              font-size: 14px;
              opacity: 0.9;
              margin-top: 5px;
              font-weight: 400;
            }
            #report-table {
              border-collapse: collapse;
              width: 100%;
              background: #fff;
            }
            .period-header {
              background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
              color: #fff;
              font-size: 18px;
              font-weight: 600;
              padding: 15px 20px;
              text-align: left;
              border: none;
              text-transform: uppercase;
              letter-spacing: 1px;
            }
            .date-range {
              background: #34495e;
              color: #ecf0f1;
              font-size: 13px;
              padding: 10px 20px;
              border: none;
              font-weight: 500;
              letter-spacing: 0.3px;
            }
            .metric-header {
              background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
              color: #fff;
              font-weight: 600;
              padding: 12px 15px;
              text-align: center;
              border: none;
              font-size: 14px;
            }
            .metric-row {
              border-bottom: 1px solid #ecf0f1;
            }
            .metric-row:hover {
              background: #f8f9fa;
            }
            .metric-row td {
              padding: 12px 15px;
              border: none;
              font-size: 14px;
            }
            .metric-name {
              font-weight: 600;
              color: #2c3e50;
              background: #f8f9fa;
            }
            .metric-value {
              text-align: right;
              font-weight: 500;
              color: #34495e;
            }
            .change-positive {
              color: #27ae60;
              font-weight: 600;
            }
            .change-negative {
              color: #e74c3c;
              font-weight: 600;
            }
            .change-neutral {
              color: #95a5a6;
              font-weight: 600;
            }
          </style>
        </head>
        <body>
          <div class="report-container">
            <div class="report-header">
              Daily Sales Report - ${data.reportDate}
              <div class="report-subtitle">Generated at ${data.reportTime}</div>
            </div>
            <table id="report-table">
              ${tableRows}
            </table>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate and send daily sales report with period summaries (sent at midnight AEST)
   */
  async sendDailySalesReport(): Promise<void> {
    try {
      if (!config.salesReportingEnabled) {
        logger.info('Sales reporting is disabled, skipping daily report');
        return;
      }

      logger.info('Generating new daily sales report with period summaries');

      const reportData = await this.generateNewDailyReportData();
      const slackBlocks = this.formatNewDailySlackBlocks(reportData);

      await this.sendToSlack(slackBlocks, 'daily');

      logger.info(`Daily sales report sent successfully for ${reportData.reportDate}`);
    } catch (error) {
      logger.error('Failed to send daily sales report:', error);
      throw error;
    }
  }

  /**
   * Generate and send legacy daily sales report (kept for backward compatibility)
   */
  async sendLegacyDailySalesReport(): Promise<void> {
    try {
      if (!config.salesReportingEnabled) {
        logger.info('Sales reporting is disabled, skipping daily report');
        return;
      }

      const yesterday = DateTime.now().setZone('Australia/Sydney').minus({ days: 1 });
      const reportDate = yesterday.toFormat('yyyy-MM-dd');

      logger.info(`Generating legacy daily sales report for ${reportDate}`);

      const reportData = await this.generateDailySalesData(reportDate);
      const slackBlocks = this.formatDailySlackBlocks(reportData);

      await this.sendToSlack(slackBlocks, 'daily');

      logger.info(`Legacy daily sales report sent successfully for ${reportDate}`);
    } catch (error) {
      logger.error('Failed to send legacy daily sales report:', error);
      throw error;
    }
  }

  /**
   * Generate and send weekly sales report for the previous week
   */
  async sendWeeklySalesReport(): Promise<void> {
    try {
      if (!config.salesReportingEnabled) {
        logger.info('Sales reporting is disabled, skipping weekly report');
        return;
      }

      const now = DateTime.now().setZone('Australia/Sydney');
      const lastWeekStart = now.startOf('week').minus({ weeks: 1 });
      
      logger.info(`Generating weekly sales report for week starting ${lastWeekStart.toFormat('yyyy-MM-dd')}`);
      
      const reportData = await this.generateWeeklySalesData(lastWeekStart);
      const slackBlocks = this.formatWeeklySlackBlocks(reportData);

      await this.sendToSlack(slackBlocks, 'weekly');
      
      logger.info(`Weekly sales report sent successfully for week ${reportData.weekNumber}`);
    } catch (error) {
      logger.error('Failed to send weekly sales report:', error);
      throw error;
    }
  }

  /**
   * Generate and send monthly sales report for the previous month
   */
  async sendMonthlySalesReport(): Promise<void> {
    try {
      if (!config.salesReportingEnabled) {
        logger.info('Sales reporting is disabled, skipping monthly report');
        return;
      }

      const now = DateTime.now().setZone('Australia/Sydney');
      const lastMonthStart = now.startOf('month').minus({ months: 1 });
      
      logger.info(`Generating monthly sales report for ${lastMonthStart.toFormat('MMMM yyyy')}`);
      
      const reportData = await this.generateMonthlySalesData(lastMonthStart);
      const slackBlocks = this.formatMonthlySlackBlocks(reportData);

      await this.sendToSlack(slackBlocks, 'monthly');
      
      logger.info(`Monthly sales report sent successfully for ${lastMonthStart.toFormat('MMMM yyyy')}`);
    } catch (error) {
      logger.error('Failed to send monthly sales report:', error);
      throw error;
    }
  }

  /**
   * Generate daily sales data for a specific date
   */
  private async generateDailySalesData(date: string): Promise<DailySalesData> {
    const client = await db.connect();

    try {
      const startDate = `${date} 00:00:00`;
      const endDate = `${date} 23:59:59`;

      // Get payment statistics
      const statsQuery = `
        SELECT
          COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as successful_payments,
          COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_amount_cents
        FROM stripe_payments
        WHERE created_at >= $1 AND created_at <= $2
      `;

      const statsResult = await client.query(statsQuery, [startDate, endDate]);
      const stats = statsResult.rows[0];

      // Get customer payment details (successful payments only)
      const paymentsQuery = `
        SELECT
          customer_name,
          customer_email,
          status,
          description,
          created_at,
          amount_cents
        FROM stripe_payments
        WHERE created_at >= $1 AND created_at <= $2 AND status LIKE '%succeeded%'
        ORDER BY created_at DESC
      `;

      const paymentsResult = await client.query(paymentsQuery, [startDate, endDate]);

      const customers: CustomerPayment[] = paymentsResult.rows.map(row => ({
        name: row.customer_name || 'Unknown',
        email: row.customer_email,
        status: 'succeeded', // Only successful payments are fetched
        description: row.description || '',
        timestamp: DateTime.fromJSDate(row.created_at).setZone('Australia/Sydney').toFormat('HH:mm'),
        amountCents: row.amount_cents
      }));

      const successfulPayments = parseInt(stats.successful_payments, 10);

      return {
        date,
        totalPayments: successfulPayments,
        successfulPayments,
        totalAmountCents: parseInt(stats.total_amount_cents, 10),
        customers
      };
    } finally {
      client.release();
    }
  }

  /**
   * Generate weekly sales data with daily breakdown
   */
  private async generateWeeklySalesData(weekStart: DateTime): Promise<WeeklySalesData> {
    const weekEnd = weekStart.plus({ days: 6 });
    const weekNumber = weekStart.weekNumber;

    // Generate daily breakdown for the week
    const dailyBreakdown: DailySalesData[] = [];
    let currentDate = weekStart;

    while (currentDate <= weekEnd) {
      const dailyData = await this.generateDailySalesData(currentDate.toFormat('yyyy-MM-dd'));
      dailyBreakdown.push(dailyData);
      currentDate = currentDate.plus({ days: 1 });
    }

    // Calculate week totals
    const weekTotals = dailyBreakdown.reduce(
      (totals, day) => ({
        totalPayments: totals.totalPayments + day.totalPayments,
        successfulPayments: totals.successfulPayments + day.successfulPayments,
        totalAmountCents: totals.totalAmountCents + day.totalAmountCents,
        customers: [...totals.customers, ...day.customers]
      }),
      { totalPayments: 0, successfulPayments: 0, totalAmountCents: 0, customers: [] as CustomerPayment[] }
    );

    // Get comparison data for previous week
    // const previousWeekStart = weekStart.minus({ weeks: 1 });
    // const previousWeekData = await this.generateWeekTotals(previousWeekStart);
    // const comparison = this.calculateComparison(weekTotals, previousWeekData);

    return {
      date: weekStart.toFormat('yyyy-MM-dd'),
      weekStart: weekStart.toFormat('yyyy-MM-dd'),
      weekEnd: weekEnd.toFormat('yyyy-MM-dd'),
      weekNumber,
      totalPayments: weekTotals.totalPayments,
      successfulPayments: weekTotals.successfulPayments,
      totalAmountCents: weekTotals.totalAmountCents,
      customers: weekTotals.customers,
      dailyBreakdown,
      // comparisonToPreviousWeek: comparison
    };
  }

  /**
   * Generate monthly sales data with weekly breakdown
   */
  private async generateMonthlySalesData(monthStart: DateTime): Promise<MonthlySalesData> {
    const monthEnd = monthStart.endOf('month');

    // Generate weekly breakdown - simple 7-day periods starting from month start
    const weeklyBreakdown: WeeklySalesData[] = [];
    let currentStart = monthStart;

    while (currentStart <= monthEnd) {
      // Each "week" is 7 days or until end of month
      const currentEnd = currentStart.plus({ days: 6 });
      const actualEnd = currentEnd > monthEnd ? monthEnd : currentEnd;

      const weekData = await this.generateWeeklyDataForRange(currentStart, actualEnd);
      weeklyBreakdown.push(weekData);

      // Move to next 7-day period
      currentStart = currentStart.plus({ days: 7 });
    }

    // Calculate month totals
    const monthTotals = weeklyBreakdown.reduce(
      (totals, week) => ({
        totalPayments: totals.totalPayments + week.totalPayments,
        successfulPayments: totals.successfulPayments + week.successfulPayments,
        totalAmountCents: totals.totalAmountCents + week.totalAmountCents,
        customers: [...totals.customers, ...week.customers]
      }),
      { totalPayments: 0, successfulPayments: 0, totalAmountCents: 0, customers: [] as CustomerPayment[] }
    );

    // Get comparison data for previous month
    // const previousMonthStart = monthStart.minus({ months: 1 });
    // const previousMonthData = await this.generateMonthTotals(previousMonthStart);
    // const comparison = this.calculateComparison(monthTotals, previousMonthData);

    return {
      date: monthStart.toFormat('yyyy-MM-dd'),
      monthStart: monthStart.toFormat('yyyy-MM-dd'),
      monthEnd: monthEnd.toFormat('yyyy-MM-dd'),
      totalPayments: monthTotals.totalPayments,
      successfulPayments: monthTotals.successfulPayments,
      totalAmountCents: monthTotals.totalAmountCents,
      customers: monthTotals.customers,
      weeklyBreakdown,
      // comparisonToPreviousMonth: comparison
    };
  }

  // /**
  //  * Calculate comparison between current and previous period
  //  */
  // private calculateComparison(current: { totalPayments: number; totalAmountCents: number }, previous: { totalPayments: number; totalAmountCents: number }): ComparisonData {
  //   const paymentCountChange = current.totalPayments - previous.totalPayments;
  //   const paymentCountPercentChange = previous.totalPayments > 0 ?
  //     ((current.totalPayments - previous.totalPayments) / previous.totalPayments) * 100 : 0;
  //
  //   const amountChange = current.totalAmountCents - previous.totalAmountCents;
  //   const amountPercentChange = previous.totalAmountCents > 0 ?
  //     ((current.totalAmountCents - previous.totalAmountCents) / previous.totalAmountCents) * 100 : 0;

  //   return {
  //     paymentCountChange,
  //     paymentCountPercentChange,
  //     amountChange,
  //     amountPercentChange
  //   };
  // }

  // /**
  //  * Helper method to get week totals for comparison
  //  */
  // private async generateWeekTotals(weekStart: DateTime): Promise<{ totalPayments: number; successfulPayments: number; totalAmountCents: number }> {
  //   const weekEnd = weekStart.plus({ days: 6 });
  //   const client = await db.connect();

  //   try {
  //     const query = `
  //       SELECT
  //         COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as successful_payments,
  //         COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_amount_cents
  //       FROM stripe_payments
  //       WHERE created_at >= $1 AND created_at <= $2
  //     `;

  //     const result = await client.query(query, [
  //       weekStart.toFormat('yyyy-MM-dd 00:00:00'),
  //       weekEnd.toFormat('yyyy-MM-dd 23:59:59')
  //     ]);

  //     const stats = result.rows[0];
  //     const successfulPayments = parseInt(stats.successful_payments, 10);

  //     return {
  //       totalPayments: successfulPayments,
  //       successfulPayments,
  //       totalAmountCents: parseInt(stats.total_amount_cents, 10)
  //     };
  //   } finally {
  //     client.release();
  //   }
  // }

  /**
   * Generate weekly data for a specific date range (used for monthly reports)
   */
  private async generateWeeklyDataForRange(weekStart: DateTime, weekEnd: DateTime): Promise<WeeklySalesData> {
    const weekNumber = weekStart.weekNumber;

    // Generate daily breakdown for the week range
    const dailyBreakdown: DailySalesData[] = [];
    let currentDate = weekStart;

    while (currentDate <= weekEnd) {
      const dailyData = await this.generateDailySalesData(currentDate.toFormat('yyyy-MM-dd'));
      dailyBreakdown.push(dailyData);
      currentDate = currentDate.plus({ days: 1 });
    }

    // Calculate week totals
    const weekTotals = dailyBreakdown.reduce(
      (totals, day) => ({
        totalPayments: totals.totalPayments + day.totalPayments,
        successfulPayments: totals.successfulPayments + day.successfulPayments,
        totalAmountCents: totals.totalAmountCents + day.totalAmountCents,
        customers: [...totals.customers, ...day.customers]
      }),
      { totalPayments: 0, successfulPayments: 0, totalAmountCents: 0, customers: [] as CustomerPayment[] }
    );

    // Get comparison data for previous week (same duration)
    // const previousWeekStart = weekStart.minus({ weeks: 1 });
    // const previousWeekEnd = weekEnd.minus({ weeks: 1 });
    // const previousWeekData = await this.generateWeekTotalsForRange(previousWeekStart, previousWeekEnd);
    // const comparison = this.calculateComparison(weekTotals, previousWeekData);

    return {
      date: weekStart.toFormat('yyyy-MM-dd'),
      weekStart: weekStart.toFormat('yyyy-MM-dd'),
      weekEnd: weekEnd.toFormat('yyyy-MM-dd'),
      weekNumber,
      totalPayments: weekTotals.totalPayments,
      successfulPayments: weekTotals.successfulPayments,
      totalAmountCents: weekTotals.totalAmountCents,
      customers: weekTotals.customers,
      dailyBreakdown,
      // comparisonToPreviousWeek: comparison
    };
  }

  // /**
  //  * Helper method to get week totals for a specific date range
  //  */
  // private async generateWeekTotalsForRange(weekStart: DateTime, weekEnd: DateTime): Promise<{ totalPayments: number; successfulPayments: number; totalAmountCents: number; successRate: number }> {
  //   const client = await db.connect();

  //   try {
  //     const query = `
  //       SELECT
  //         COUNT(*) as total_payments,
  //         COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as successful_payments,
  //         COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_amount_cents
  //       FROM stripe_payments
  //       WHERE created_at >= $1 AND created_at <= $2
  //     `;

  //     const result = await client.query(query, [
  //       weekStart.toFormat('yyyy-MM-dd 00:00:00'),
  //       weekEnd.toFormat('yyyy-MM-dd 23:59:59')
  //     ]);

  //     const stats = result.rows[0];
  //     const totalPayments = parseInt(stats.total_payments, 10);
  //     const successfulPayments = parseInt(stats.successful_payments, 10);
  //     const successRate = totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0;

  //     return {
  //       totalPayments,
  //       successfulPayments,
  //       totalAmountCents: parseInt(stats.total_amount_cents, 10),
  //       successRate
  //     };
  //   } finally {
  //     client.release();
  //   }
  // }

  // /**
  //  * Helper method to get month totals for comparison
  //  */
  // private async generateMonthTotals(monthStart: DateTime): Promise<{ totalPayments: number; successfulPayments: number; totalAmountCents: number }> {
  //   const monthEnd = monthStart.endOf('month');
  //   const client = await db.connect();

  //   try {
  //     const query = `
  //       SELECT
  //         COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as successful_payments,
  //         COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_amount_cents
  //       FROM stripe_payments
  //       WHERE created_at >= $1 AND created_at <= $2
  //     `;

  //     const result = await client.query(query, [
  //       monthStart.toFormat('yyyy-MM-dd 00:00:00'),
  //       monthEnd.toFormat('yyyy-MM-dd 23:59:59')
  //     ]);

  //     const stats = result.rows[0];
  //     const successfulPayments = parseInt(stats.successful_payments, 10);

  //     return {
  //       totalPayments: successfulPayments,
  //       successfulPayments,
  //       totalAmountCents: parseInt(stats.total_amount_cents, 10)
  //     };
  //   } finally {
  //     client.release();
  //   }
  // }

  /**
   * Calculate period data for a specific time range
   */
  private async calculatePeriodData(startDate: DateTime, endDate: DateTime): Promise<PeriodData> {
    const client = await db.connect();

    try {
      const query = `
        SELECT
          COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as sales_count,
          COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_revenue_cents
        FROM stripe_payments
        WHERE created_at >= $1 AND created_at <= $2
      `;

      const result = await client.query(query, [
        startDate.toUTC().toFormat('yyyy-MM-dd HH:mm:ss'),
        endDate.toUTC().toFormat('yyyy-MM-dd HH:mm:ss')
      ]);

      const stats = result.rows[0];
      const salesCount = parseInt(stats.sales_count, 10);
      const totalRevenueCents = parseInt(stats.total_revenue_cents, 10);
      const averageOrderValueCents = salesCount > 0 ? Math.round(totalRevenueCents / salesCount) : 0;

      return {
        salesCount,
        totalRevenueCents,
        averageOrderValueCents
      };
    } finally {
      client.release();
    }
  }

  /**
   * Generate new daily sales report data with period summaries
   */
  async generateNewDailyReportData(): Promise<NewDailyReportData> {
    const now = DateTime.now().setZone('Australia/Sydney');
    const reportDate = now.toFormat('yyyy-MM-dd');
    const reportTime = now.toFormat('HH:mm ZZZZ');

    // Calculate current periods
    // For 'Past 24 hrs', use the full previous calendar day (yesterday)
    const startOfYesterday = now.minus({ days: 1 }).startOf('day');
    const endOfYesterday = now.minus({ days: 1 }).endOf('day');
    const startOfDayBeforeYesterday = now.minus({ days: 2 }).startOf('day');
    const endOfDayBeforeYesterday = now.minus({ days: 2 }).endOf('day');

    const past24hCurrent = await this.calculatePeriodData(
      startOfYesterday,
      endOfYesterday
    );

    // For previous period, use the day before yesterday
    const past24hPrevious = await this.calculatePeriodData(
      startOfDayBeforeYesterday,
      endOfDayBeforeYesterday
    );

    // Keep 7 and 28 day periods as rolling windows
    const past7dCurrent = await this.calculatePeriodData(
      now.minus({ days: 7 }),
      now
    );

    const past28dCurrent = await this.calculatePeriodData(
      now.minus({ days: 28 }),
      now
    );

    const past7dPrevious = await this.calculatePeriodData(
      now.minus({ days: 14 }),
      now.minus({ days: 7 })
    );

    const past28dPrevious = await this.calculatePeriodData(
      now.minus({ days: 56 }),
      now.minus({ days: 28 })
    );

    // Create period summaries
    const periodSummaries: PeriodSummary[] = [
      this.createPeriodSummary('Past 24 hrs', past24hCurrent, past24hPrevious),
      this.createPeriodSummary('Past 7 days', past7dCurrent, past7dPrevious),
      this.createPeriodSummary('Past 28 days', past28dCurrent, past28dPrevious)
    ];

    return {
      reportDate,
      reportTime,
      periodSummaries
    };
  }

  /**
   * Create a period summary with comparisons
   */
  private createPeriodSummary(period: string, current: PeriodData, previous: PeriodData): PeriodSummary {
    const deltaOrders = current.salesCount - previous.salesCount;
    const deltaOrdersPercent = previous.salesCount > 0 ?
      ((current.salesCount - previous.salesCount) / previous.salesCount) * 100 : 0;

    const currentAvgDollars = current.averageOrderValueCents / 100;
    const previousAvgDollars = previous.averageOrderValueCents / 100;
    const deltaAvgOrderValue = currentAvgDollars - previousAvgDollars;
    const deltaAvgOrderValuePercent = previous.averageOrderValueCents > 0 ?
      ((current.averageOrderValueCents - previous.averageOrderValueCents) / previous.averageOrderValueCents) * 100 : 0;

    return {
      period,
      orders: current.salesCount,
      avgOrderValue: currentAvgDollars,
      previousPeriodOrders: previous.salesCount,
      previousAvgOrderValue: previousAvgDollars,
      deltaOrders,
      deltaOrdersPercent,
      deltaAvgOrderValue,
      deltaAvgOrderValuePercent
    };
  }

  /**
   * Format new daily sales report into Slack blocks
   */
  private formatNewDailySlackBlocks(data: NewDailyReportData): (Block | KnownBlock)[] {
    const blocks: (Block | KnownBlock)[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📊 Daily Sales Report (sent at midnight AEST)',
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Report Date:* ${data.reportDate} at ${data.reportTime}`
        }
      },
      { type: 'divider' },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*📋 Period Summary*'
        }
      },
      { type: 'divider' }
    ];

    // For each period, create a neat, aligned code block table
    data.periodSummaries.forEach(summary => {
      // Calculate total sales for current and previous periods
      const totalSalesCurrent = summary.orders * summary.avgOrderValue;
      const totalSalesPrevious = summary.previousPeriodOrders * summary.previousAvgOrderValue;
      const totalSalesChangePercent = totalSalesPrevious !== 0 ? (totalSalesCurrent - totalSalesPrevious) / totalSalesPrevious * 100 : 0;

      // Format values
      const ordersChangePercent = summary.previousPeriodOrders !== 0 ? ((summary.orders - summary.previousPeriodOrders) / summary.previousPeriodOrders) * 100 : 0;
      const avgOrderChangePercent = summary.previousAvgOrderValue !== 0 ? ((summary.avgOrderValue - summary.previousAvgOrderValue) / summary.previousAvgOrderValue) * 100 : 0;

      // Build change columns as percentage only, right-aligned
      const ordersChangePctStr = (ordersChangePercent >= 0 ? '+' : '') + ordersChangePercent.toFixed(1) + '%';
      const ordersChangeCol = ordersChangePctStr.padStart(10);

      const avgOrderChangePctStr = (avgOrderChangePercent >= 0 ? '+' : '') + avgOrderChangePercent.toFixed(1) + '%';
      const avgOrderChangeCol = avgOrderChangePctStr.padStart(10);

      const totalSalesChangePctStr = (totalSalesChangePercent >= 0 ? '+' : '') + totalSalesChangePercent.toFixed(1) + '%';
      const totalSalesChangeCol = totalSalesChangePctStr.padStart(10);

      // Table header and rows with neat alignment, previous period first
      let table = '';
      table += 'Metric'.padEnd(18) + 'Previous Period'.padEnd(18) + 'Current Period'.padEnd(18) + 'Change'.padEnd(10) + '\n';
      table += '-'.repeat(64) + '\n';
      table += 'Orders'.padEnd(18) +
        summary.previousPeriodOrders.toLocaleString().padStart(14) + '   ' +
        summary.orders.toLocaleString().padStart(14) + '   ' +
        ordersChangeCol + '\n';
      table += 'Avg Order Value'.padEnd(18) +
        ('$' + summary.previousAvgOrderValue.toFixed(2)).padStart(14) + '   ' +
        ('$' + summary.avgOrderValue.toFixed(2)).padStart(14) + '   ' +
        avgOrderChangeCol + '\n';
      table += 'Total Sales'.padEnd(18) +
        ('$' + totalSalesPrevious.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})).padStart(14) + '   ' +
        ('$' + totalSalesCurrent.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})).padStart(14) + '   ' +
        totalSalesChangeCol + '\n';
      table += '\n'; // Blank line after each table

      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*📅 ${summary.period.replace('Past ', 'Past ')}*\n` +
            '```' + table + '```'
        }
      });
    });

    // Add summary insights
    const totalCurrentOrders = data.periodSummaries.reduce((sum, p) => sum + p.orders, 0);
    const totalPreviousOrders = data.periodSummaries.reduce((sum, p) => sum + p.previousPeriodOrders, 0);
    const overallOrderChange = totalPreviousOrders > 0 ?
      ((totalCurrentOrders - totalPreviousOrders) / totalPreviousOrders) * 100 : 0;

    const overallTrend = overallOrderChange >= 0 ? '📈' : '📉';
    const overallChangeText = `${overallOrderChange >= 0 ? '+' : ''}${overallOrderChange.toFixed(1)}%`;

    blocks.push(
      { type: 'divider' },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*📈 Overall Trend:* ${overallTrend} ${overallChangeText} across all periods`
        }
      }
    );

    return blocks;
  }

/**
   * Format daily sales data into Slack blocks
   */
  private formatDailySlackBlocks(data: DailySalesData): (Block | KnownBlock)[] {
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');
    const reportDate = DateTime.fromISO(data.date).toFormat('dd MMM yyyy');

    const blocks: (Block | KnownBlock)[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '💰 Daily Sales Report',
          emoji: true,
        },
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Generated: ${timestamp} | Report Date: ${reportDate}`,
          },
        ],
      },
      { type: 'divider' },
    ];

    // Summary section
    const totalAmountText = data.totalAmountCents > 0 ? `$${(data.totalAmountCents / 100).toFixed(2)}` : '$0.00';

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Daily Summary for ${reportDate}*\n` +
              `• Total Payments: ${data.totalPayments}\n` +
              `• Total Revenue: ${totalAmountText}`,
      },
    });

    // Customer details (if any payments)
    if (data.customers.length > 0) {
      blocks.push({ type: 'divider' });
      
      let customerText = '*Payments:*\n';
      data.customers.forEach(customer => {
        const amount = `$${(customer.amountCents / 100).toFixed(2)}`;
        customerText += `✅ ${customer.name} (${customer.email}) - ${amount} at ${customer.timestamp}\n`;
      });

      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: customerText,
        },
      });
    } else {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*No payments recorded for this day.*',
        },
      });
    }

    // Footer
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `_Automated daily report • ${config.devMode ? 'Development' : 'Production'} environment_`,
        },
      ],
    });

    return blocks;
  }

  /**
   * Format weekly sales data into Slack blocks
   */
  private formatWeeklySlackBlocks(data: WeeklySalesData): (Block | KnownBlock)[] {
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');
    const weekStartFormatted = DateTime.fromISO(data.weekStart).toFormat('dd MMM');
    const weekEndFormatted = DateTime.fromISO(data.weekEnd).toFormat('dd MMM yyyy');

    const blocks: (Block | KnownBlock)[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📊 Weekly Sales Report',
          emoji: true,
        },
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Generated: ${timestamp} | Week ${data.weekNumber}: ${weekStartFormatted} - ${weekEndFormatted}`,
          },
        ],
      },
      { type: 'divider' },
    ];

    // Weekly summary
    const totalAmountText = data.totalAmountCents > 0 ? `$${(data.totalAmountCents / 100).toFixed(2)}` : '$0.00';

    // Comparison indicators
    // const paymentTrend = data.comparisonToPreviousWeek.paymentCountChange >= 0 ? '📈' : '📉';
    // const paymentChangeText = data.comparisonToPreviousWeek.paymentCountPercentChange !== 0 ?
    //   ` (${data.comparisonToPreviousWeek.paymentCountChange >= 0 ? '+' : ''}${data.comparisonToPreviousWeek.paymentCountPercentChange.toFixed(1)}%)` : '';

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Weekly Summary*\n` +
              `• Total Payments: ${data.totalPayments}\n` +
              `• Total Revenue: ${totalAmountText}`,
      },
    });

    // Daily breakdown table
    blocks.push({ type: 'divider' });
    
    let tableText = `*Daily Breakdown:*\n\`\`\`\n`;
    tableText += `Day       Date     Payments  Revenue\n`;
    tableText += `─────────────────────────────────\n`;

    data.dailyBreakdown.forEach(day => {
      const dayDate = DateTime.fromISO(day.date);
      const dayName = dayDate.toFormat('ccc').padEnd(9);
      const dateFormatted = dayDate.toFormat('MMM dd').padEnd(8);
      const paymentsText = day.totalPayments.toString().padEnd(9);
      const revenueText = `$${(day.totalAmountCents / 100).toFixed(0)}`;

      tableText += `${dayName} ${dateFormatted} ${paymentsText} ${revenueText}\n`;
    });

    tableText += `\`\`\``;

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: tableText,
      },
    });

    // Footer
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `_Automated weekly report • ${config.devMode ? 'Development' : 'Production'} environment_`,
        },
      ],
    });

    return blocks;
  }

  /**
   * Format monthly sales data into Slack blocks
   */
  private formatMonthlySlackBlocks(data: MonthlySalesData): (Block | KnownBlock)[] {
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');
    const monthName = DateTime.fromISO(data.monthStart).toFormat('MMMM yyyy');

    const blocks: (Block | KnownBlock)[] = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📈 Monthly Sales Report',
          emoji: true,
        },
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: `Generated: ${timestamp} | Month: ${monthName}`,
          },
        ],
      },
      { type: 'divider' },
    ];

    // Monthly summary with comparisons
    const totalAmountText = data.totalAmountCents > 0 ? `$${(data.totalAmountCents / 100).toFixed(2)}` : '$0.00';

    // const paymentTrend = data.comparisonToPreviousMonth.paymentCountChange >= 0 ? '📈' : '📉';
    // const paymentChangeText = data.comparisonToPreviousMonth.paymentCountPercentChange !== 0 ?
    //   ` (${data.comparisonToPreviousMonth.paymentCountChange >= 0 ? '+' : ''}${data.comparisonToPreviousMonth.paymentCountPercentChange.toFixed(1)}%)` : '';

    // const revenueTrend = data.comparisonToPreviousMonth.amountChange >= 0 ? '📈' : '📉';
    // const revenueChangeText = data.comparisonToPreviousMonth.amountPercentChange !== 0 ?
    //   ` (${data.comparisonToPreviousMonth.amountChange >= 0 ? '+' : ''}${data.comparisonToPreviousMonth.amountPercentChange.toFixed(1)}%)` : '';

    blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Monthly Summary for ${monthName}*\n` +
              `• Total Payments: ${data.totalPayments}\n` +
              `• Total Revenue: ${totalAmountText}`,
      },
    });

    // Weekly breakdown
    if (data.weeklyBreakdown.length > 0) {
      blocks.push({ type: 'divider' });

      let weeklyText = `*Weekly Breakdown:*\n\`\`\`\n`;
      weeklyText += `Period      Dates           Payments  Revenue\n`;
      weeklyText += `──────────────────────────────────────────\n`;

      data.weeklyBreakdown.forEach((week, index) => {
        const weekStart = DateTime.fromISO(week.weekStart);
        const weekEnd = DateTime.fromISO(week.weekEnd);

        // Create a more descriptive period label
        const periodLabel = `Week ${index + 1}`.padEnd(11);
        const startDate = weekStart.toFormat('MMM dd');
        const endDate = weekEnd.toFormat('MMM dd');
        const dates = `${startDate}-${endDate}`.padEnd(15);
        const payments = week.totalPayments.toString().padEnd(9);
        const revenue = `$${(week.totalAmountCents / 100).toFixed(0)}`;

        weeklyText += `${periodLabel} ${dates} ${payments} ${revenue}\n`;
      });

      weeklyText += `\`\`\``;

      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: weeklyText,
        },
      });
    }

    // Footer
    blocks.push({
      type: 'context',
      elements: [
        {
          type: 'mrkdwn',
          text: `_Automated monthly report • ${config.devMode ? 'Development' : 'Production'} environment_`,
        },
      ],
    });

    return blocks;
  }

  /**
   * Send formatted blocks to Slack
   */
  private async sendToSlack(blocks: (Block | KnownBlock)[], reportType: string): Promise<void> {
    const slackChannel = config.slackSalesReportChannel;

    if (!slackChannel) {
      const warningMsg = 'No Slack sales report channel configured (SLACK_SALES_REPORT_CHANNEL)';
      logger.warn(warningMsg);
      throw new Error(warningMsg);
    }

    if (!config.slackToken) {
      const errorMsg = 'No Slack token configured (SLACK_TOKEN)';
      logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    try {
      const response = await this.slack.chat.postMessage({
        channel: slackChannel,
        blocks: blocks,
        text: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Sales Report`, // Fallback text for notifications
        username: 'Sales Report Bot',
        icon_emoji: ':money_with_wings:'
      });

      if (!response.ok) {
        throw new Error(`Slack API error: ${response.error || 'Unknown error'}`);
      }

      logger.info(`${reportType} sales report sent to Slack successfully`);
    } catch (error) {
      logger.error(`Failed to send ${reportType} sales report to Slack:`, error);
      throw error;
    }
  }

  /**
   * Store payment data from Stripe webhook (used by webhook handler)
   */
  async storePaymentData(paymentData: StripePaymentData): Promise<void> {
    const client = await db.connect();

    try {
      await client.query('BEGIN');

      const query = `
        INSERT INTO stripe_payments 
        (stripe_payment_intent_id, customer_name, customer_email, amount_cents, currency, status, description, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (stripe_payment_intent_id) 
        DO UPDATE SET 
          status = EXCLUDED.status,
          processed_at = CURRENT_TIMESTAMP
      `;

      await client.query(query, [
        paymentData.stripePaymentIntentId,
        paymentData.customerName,
        paymentData.customerEmail,
        paymentData.amountCents,
        paymentData.currency,
        paymentData.status,
        paymentData.description,
        JSON.stringify(paymentData.metadata)
      ]);

      await client.query('COMMIT');
      
      logger.info(`Payment data stored successfully for ${paymentData.stripePaymentIntentId}`);
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to store payment data:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get sales data for a specific date range (for testing/manual reports)
   */
  async getSalesDataForDateRange(startDate: string, endDate: string): Promise<DailySalesData> {
    const client = await db.connect();

    try {
      const query = `
        SELECT
          COUNT(CASE WHEN status LIKE '%succeeded%' THEN 1 END) as successful_payments,
          COALESCE(SUM(CASE WHEN status LIKE '%succeeded%' THEN amount_cents ELSE 0 END), 0) as total_amount_cents
        FROM stripe_payments
        WHERE created_at >= $1 AND created_at <= $2
      `;

      const result = await client.query(query, [
        `${startDate} 00:00:00`,
        `${endDate} 23:59:59`
      ]);

      const stats = result.rows[0];
      const successfulPayments = parseInt(stats.successful_payments, 10);

      return {
        date: startDate,
        totalPayments: successfulPayments,
        successfulPayments,
        totalAmountCents: parseInt(stats.total_amount_cents, 10),
        customers: [] // Not needed for range queries
      };
    } finally {
      client.release();
    }
  }

  /**
   * Generate sample test data for testing
   */
  async generateSampleData(days: number = 7, paymentsPerDay: number = 5, targetMonth?: string): Promise<{ message: string; recordsCreated: number }> {
    const client = await db.connect();

    try {
      await client.query('BEGIN');

      const sampleCustomers = [
        { name: 'John Smith', email: '<EMAIL>' },
        { name: 'Sarah Wilson', email: '<EMAIL>' },
        { name: 'Mike Johnson', email: '<EMAIL>' },
        { name: 'Emma Davis', email: '<EMAIL>' },
        { name: 'David Brown', email: '<EMAIL>' },
        { name: 'Lisa Garcia', email: '<EMAIL>' },
        { name: 'Tom Anderson', email: '<EMAIL>' },
        { name: 'Amy Taylor', email: '<EMAIL>' }
      ];

      const paymentStatuses = ['payment_intent.succeeded', 'payment_intent.succeeded', 'payment_intent.succeeded', 'payment_intent.failed'];
      const descriptions = ['Consultation Hold Fee', 'Treatment Plan Payment', 'Follow-up Consultation', 'Initial Assessment'];

      let recordsCreated = 0;

      // If targetMonth is specified, generate data for that specific month
      if (targetMonth) {
        const monthDate = DateTime.fromISO(targetMonth).setZone('Australia/Sydney');
        const monthStart = monthDate.startOf('month');
        const monthEnd = monthDate.endOf('month');
        const daysInMonth = monthEnd.day;

        for (let day = 1; day <= daysInMonth; day++) {
          const targetDate = monthStart.set({ day });

          // Vary payments per day (weekends have fewer payments)
          const isWeekend = targetDate.weekday >= 6;
          const dailyPayments = isWeekend ? Math.floor(paymentsPerDay * 0.3) : paymentsPerDay;

          for (let payment = 0; payment < dailyPayments; payment++) {
            const customer = sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)];
            const status = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
            const description = descriptions[Math.floor(Math.random() * descriptions.length)];
            const amount = 2999; // $29.99 in cents

            // Random time during business hours (9 AM - 6 PM)
            const hour = Math.floor(Math.random() * 9) + 9; // 9-17
            const minute = Math.floor(Math.random() * 60);
            const paymentTime = targetDate.set({ hour, minute, second: 0, millisecond: 0 });

            const paymentIntentId = `pi_${targetMonth.replace('-', '')}_${day}_${payment}_${Math.random().toString(36).substring(2, 11)}`;

            const query = `
              INSERT INTO stripe_payments
              (stripe_payment_intent_id, customer_name, customer_email, amount_cents, currency, status, description, metadata, created_at)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
              ON CONFLICT (stripe_payment_intent_id) DO NOTHING
            `;

            const result = await client.query(query, [
              paymentIntentId,
              customer.name,
              customer.email,
              amount,
              'aud',
              status,
              description,
              JSON.stringify({ test_data: true, target_month: targetMonth }),
              paymentTime.toJSDate()
            ]);

            if (result.rowCount && result.rowCount > 0) {
              recordsCreated++;
            }
          }
        }
      } else {
        // Original logic for recent days
        for (let dayOffset = 0; dayOffset < days; dayOffset++) {
          const targetDate = DateTime.now().setZone('Australia/Sydney').minus({ days: dayOffset + 1 });

          for (let payment = 0; payment < paymentsPerDay; payment++) {
            const customer = sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)];
            const status = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
            const description = descriptions[Math.floor(Math.random() * descriptions.length)];
            const amount = 2999; // $29.99 in cents

            // Random time during business hours (9 AM - 6 PM)
            const hour = Math.floor(Math.random() * 9) + 9; // 9-17
            const minute = Math.floor(Math.random() * 60);
            const paymentTime = targetDate.set({ hour, minute, second: 0, millisecond: 0 });

            const paymentIntentId = `pi_test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

            const query = `
              INSERT INTO stripe_payments
              (stripe_payment_intent_id, customer_name, customer_email, amount_cents, currency, status, description, metadata, created_at)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
              ON CONFLICT (stripe_payment_intent_id) DO NOTHING
            `;

            const result = await client.query(query, [
              paymentIntentId,
              customer.name,
              customer.email,
              amount,
              'aud',
              status,
              description,
              JSON.stringify({ test_data: true }),
              paymentTime.toJSDate()
            ]);

            if (result.rowCount && result.rowCount > 0) {
              recordsCreated++;
            }
          }
        }
      }

      await client.query('COMMIT');

      logger.info(`Generated ${recordsCreated} sample payment records for testing`);

      return {
        message: `Successfully generated ${recordsCreated} sample payment records for ${days} days`,
        recordsCreated
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to generate sample data:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Send a test daily report with current data
   */
  async sendTestReport(): Promise<void> {
    try {
      const yesterday = DateTime.now().setZone('Australia/Sydney').minus({ days: 1 });
      const reportDate = yesterday.toFormat('yyyy-MM-dd');

      logger.info(`Sending test daily sales report for ${reportDate}`);

      const reportData = await this.generateDailySalesData(reportDate);
      const slackBlocks = this.formatDailySlackBlocks(reportData);

      // Add test indicator to the blocks
      slackBlocks.unshift({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '🧪 *TEST DAILY REPORT* - This is a test of the daily sales reporting system',
        },
      });

      await this.sendToSlack(slackBlocks, 'test-daily');

      logger.info('Test daily sales report sent successfully');
    } catch (error) {
      logger.error('Failed to send test daily sales report:', error);
      throw error;
    }
  }

  /**
   * Send a test weekly report
   */
  async sendTestWeeklyReport(): Promise<void> {
    try {
      const now = DateTime.now().setZone('Australia/Sydney');
      const lastWeekStart = now.startOf('week').minus({ weeks: 1 });

      logger.info(`Sending test weekly sales report for week starting ${lastWeekStart.toFormat('yyyy-MM-dd')}`);

      const reportData = await this.generateWeeklySalesData(lastWeekStart);
      const slackBlocks = this.formatWeeklySlackBlocks(reportData);

      // Add test indicator to the blocks
      slackBlocks.unshift({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '🧪 *TEST WEEKLY REPORT* - This is a test of the weekly sales reporting system',
        },
      });

      await this.sendToSlack(slackBlocks, 'test-weekly');

      logger.info('Test weekly sales report sent successfully');
    } catch (error) {
      logger.error('Failed to send test weekly sales report:', error);
      throw error;
    }
  }

  /**
   * Send a test monthly report
   */
  async sendTestMonthlyReport(): Promise<void> {
    try {
      const now = DateTime.now().setZone('Australia/Sydney');
      const lastMonthStart = now.startOf('month').minus({ months: 1 });

      logger.info(`Sending test monthly sales report for ${lastMonthStart.toFormat('MMMM yyyy')}`);

      const reportData = await this.generateMonthlySalesData(lastMonthStart);
      const slackBlocks = this.formatMonthlySlackBlocks(reportData);

      // Add test indicator to the blocks
      slackBlocks.unshift({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '🧪 *TEST MONTHLY REPORT* - This is a test of the monthly sales reporting system',
        },
      });

      await this.sendToSlack(slackBlocks, 'test-monthly');

      logger.info('Test monthly sales report sent successfully');
    } catch (error) {
      logger.error('Failed to send test monthly sales report:', error);
      throw error;
    }
  }
}

export default new SalesReportingService();
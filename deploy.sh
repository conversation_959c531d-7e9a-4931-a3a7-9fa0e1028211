#!/bin/bash
# error handling
set -e

# Ensure NVM is loaded
source ~/.nvm/nvm.sh

# Make sure Node.js version is set (optional if you need a specific version)
nvm use 18  # or the version you need

# Install pnpm if not already installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm not found. Installing pnpm..."
    npm install -g pnpm
fi

echo "Navigating to app directory"
cd /var/www/html/doc-app-mono

echo "Installing dependencies"
pnpm install  # Install dependencies using pnpm

# Install Chrome dependencies for Puppeteer (if not already installed)
if [ ! -f "/tmp/chrome-deps-installed" ]; then
    echo "Installing Chrome dependencies for Puppeteer..."
    chmod +x scripts/install-chrome-dependencies.sh
    ./scripts/install-chrome-dependencies.sh
    touch /tmp/chrome-deps-installed
    echo "Chrome dependencies installation completed"
fi

# Add DB here
pnpm run db

# Restart PM2 process
pm2 stop doc-app
pm2 delete doc-app
pm2 start pm2.config.js

echo "Deployment complete."
